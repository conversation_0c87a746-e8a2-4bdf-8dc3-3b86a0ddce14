/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:160: DeprecationWarning: `regex` has been deprecated, please use `pattern` instead
  period: str = Query("1d", regex="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:191: DeprecationWarning: `regex` has been deprecated, please use `pattern` instead
  async def get_hot_stocks(stock_type: str = Query("gainers", regex="^(gainers|losers|volume|turnover)$")):
/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend/main.py:623: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/quant/AI_Trader_V1.0/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [38719] using WatchFiles
Redis连接成功
Redis连接成功
