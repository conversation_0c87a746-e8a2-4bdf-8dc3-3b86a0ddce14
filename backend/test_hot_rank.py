#!/usr/bin/env python3
"""
测试使用stock_hot_rank_em接口获取人气榜数据
"""
import akshare as ak
import pandas as pd
import requests
import json

def test_akshare_hot_rank():
    """测试akshare的人气榜接口"""
    try:
        print("正在测试akshare人气榜接口...")
        
        # 获取人气榜数据
        hot_rank_df = ak.stock_hot_rank_em()
        print(f"获取到人气榜数据，共{len(hot_rank_df)}条记录")
        print("\n人气榜前10名:")
        print(hot_rank_df.head(10))
        
        # 获取A股实时行情数据
        print("\n正在获取A股实时行情数据...")
        spot_df = ak.stock_zh_a_spot_em()
        print(f"获取到A股实时行情数据，共{len(spot_df)}条记录")
        
        # 合并人气榜和实时行情数据
        print("\n合并数据示例:")
        for index, row in hot_rank_df.head(5).iterrows():
            code = str(row.get("代码", "")).zfill(6)
            name = row.get("名称", f"股票{code}")
            
            # 查找对应的实时行情数据
            stock_data = spot_df[spot_df['代码'] == code]
            if not stock_data.empty:
                spot_row = stock_data.iloc[0]
                current_price = float(spot_row.get("最新价", 0))
                change_percent = float(spot_row.get("涨跌幅", 0))
                turnover = spot_row.get("成交额", "0")
                
                print(f"{index + 1}. {name} ({code})")
                print(f"   价格: ¥{current_price}")
                print(f"   涨跌幅: {change_percent:+.2f}%")
                print(f"   成交额: {turnover}")
                print()
            else:
                print(f"{index + 1}. {name} ({code}) - 未找到实时行情数据")
        
        return True
        
    except Exception as e:
        print(f"测试akshare人气榜接口失败: {e}")
        return False

def test_api_endpoint():
    """测试API接口"""
    try:
        print("=" * 50)
        print("测试人气榜API接口...")
        
        url = "http://localhost:8000/api/market/popularity-ranking?limit=10"
        response = requests.get(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("API调用成功！")
            
            if 'data' in data and data['data']:
                stocks = data['data']
                print(f"\n通过API获取的人气榜前{len(stocks)}名:")
                
                for stock in stocks:
                    print(f"{stock.get('rank', 'N/A')}. {stock['name']} ({stock['code']})")
                    print(f"   价格: ¥{stock.get('current', stock.get('price', 0))}")
                    print(f"   涨跌幅: {stock.get('change_percent', 0):+.2f}%")
                    print(f"   成交额: {stock.get('turnover', 'N/A')}")
                    print()
            else:
                print("API返回数据为空")
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"测试API接口失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("测试人气榜数据获取")
    print("=" * 50)
    
    # 测试akshare接口
    if test_akshare_hot_rank():
        print("\n" + "=" * 50)
        # 测试API接口
        test_api_endpoint()
    else:
        print("akshare接口测试失败，跳过API测试")
