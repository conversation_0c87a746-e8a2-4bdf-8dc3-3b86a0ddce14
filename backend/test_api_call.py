#!/usr/bin/env python3
"""
测试API调用的脚本
"""
import requests
import json

def test_dashboard_api():
    """测试监控大屏API"""
    url = "http://localhost:8000/api/dashboard/overview"
    
    try:
        print("正在调用监控大屏API...")
        response = requests.get(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("API调用成功！")
            print(f"响应数据结构:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            
            if 'data' in data:
                dashboard_data = data['data']
                print(f"- 全球指数数据: {len(dashboard_data.get('global_indices', {}).get('亚洲', []))} 个亚洲指数")
                print(f"- 热门股票数据: {len(dashboard_data.get('hot_stocks', []))} 只股票")
                print(f"- 商品期货数据: {len(dashboard_data.get('commodities', []))} 个商品")
                print(f"- 外汇数据: {len(dashboard_data.get('forex', []))} 个货币对")
                print(f"- 板块数据: {len(dashboard_data.get('sectors', []))} 个板块")
                print(f"- 市场统计: {dashboard_data.get('market_stats', {}).get('total_stocks', 0)} 只股票")
                print(f"- 更新时间: {dashboard_data.get('update_time')}")
                
                # 显示一些具体数据
                if dashboard_data.get('global_indices', {}).get('亚洲'):
                    print("\n亚洲指数示例:")
                    for idx in dashboard_data['global_indices']['亚洲'][:2]:
                        print(f"  {idx['name']}: {idx['value']} ({idx['change_percent']:+.2f}%)")
                
                if dashboard_data.get('hot_stocks'):
                    print("\n热门股票示例:")
                    for stock in dashboard_data['hot_stocks'][:2]:
                        print(f"  {stock['name']} ({stock['code']}): {stock['current']}")
                        
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"调用API时出错: {e}")

if __name__ == "__main__":
    test_dashboard_api()
