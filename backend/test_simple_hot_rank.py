#!/usr/bin/env python3
"""
简单测试akshare人气榜接口
"""
import akshare as ak
import pandas as pd

def test_hot_rank():
    """测试人气榜接口"""
    try:
        print("正在获取人气榜数据...")
        hot_rank_df = ak.stock_hot_rank_em()
        
        print(f"获取到{len(hot_rank_df)}条记录")
        print("\n数据列名:")
        print(hot_rank_df.columns.tolist())
        
        print("\n前10名数据:")
        for index, row in hot_rank_df.head(10).iterrows():
            print(f"第{index + 1}名:")
            for col in hot_rank_df.columns:
                print(f"  {col}: {row[col]}")
            print()
            
        return hot_rank_df
        
    except Exception as e:
        print(f"获取人气榜数据失败: {e}")
        return None

if __name__ == "__main__":
    test_hot_rank()
