#!/usr/bin/env python3
"""
测试新的人气榜和成交额榜API
"""
import requests
import json

def test_popularity_ranking():
    """测试人气榜API"""
    url = "http://localhost:8000/api/market/popularity-ranking?limit=10"
    
    try:
        print("正在调用人气榜API...")
        response = requests.get(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("人气榜API调用成功！")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            
            if 'data' in data and data['data']:
                stocks = data['data']
                print(f"\n人气榜前{len(stocks)}名:")
                
                for stock in stocks:
                    print(f"{stock.get('rank', 'N/A')}. {stock['name']} ({stock['code']})")
                    print(f"   价格: ¥{stock.get('current', stock.get('price', 0))}")
                    print(f"   涨跌幅: {stock.get('change_percent', 0):+.2f}%")
                    print(f"   成交额: {stock.get('turnover', 'N/A')}")
                    print()
                    
            else:
                print("没有获取到人气榜数据")
                
        else:
            print(f"人气榜API调用失败: {response.text}")
            
    except Exception as e:
        print(f"调用人气榜API时出错: {e}")

def test_turnover_ranking():
    """测试成交额榜API"""
    url = "http://localhost:8000/api/market/turnover-ranking?limit=10"
    
    try:
        print("正在调用成交额榜API...")
        response = requests.get(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("成交额榜API调用成功！")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            
            if 'data' in data and data['data']:
                stocks = data['data']
                print(f"\n成交额榜前{len(stocks)}名:")
                
                for stock in stocks:
                    print(f"{stock.get('rank', 'N/A')}. {stock['name']} ({stock['code']})")
                    print(f"   价格: ¥{stock.get('current', stock.get('price', 0))}")
                    print(f"   涨跌幅: {stock.get('change_percent', 0):+.2f}%")
                    print(f"   成交额: {stock.get('turnover', 'N/A')}")
                    print()
                    
            else:
                print("没有获取到成交额榜数据")
                
        else:
            print(f"成交额榜API调用失败: {response.text}")
            
    except Exception as e:
        print(f"调用成交额榜API时出错: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("测试新的排行榜API")
    print("=" * 50)
    
    test_popularity_ranking()
    print("\n" + "=" * 50)
    test_turnover_ranking()
