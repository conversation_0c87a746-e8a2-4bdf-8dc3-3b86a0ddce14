#!/usr/bin/env python3
"""
测试热门股票数据的脚本
"""
import requests
import json

def test_hot_stocks_api():
    """测试热门股票API"""
    url = "http://localhost:8000/api/market/hot-stocks"
    
    try:
        print("正在调用热门股票API...")
        response = requests.get(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("API调用成功！")
            print(f"响应数据结构:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            
            if 'data' in data and data['data']:
                hot_stocks = data['data']
                print(f"\n热门股票数据 ({len(hot_stocks)} 只):")
                
                for i, stock in enumerate(hot_stocks, 1):
                    print(f"{i}. {stock['name']} ({stock['code']})")
                    print(f"   价格: ¥{stock.get('current', stock.get('price', 0))}")
                    print(f"   涨跌: {stock.get('change', 0):+.2f}")
                    print(f"   涨跌幅: {stock.get('change_percent', 0):+.2f}%")
                    print(f"   成交量: {stock.get('volume', 'N/A')}")
                    print(f"   成交额: {stock.get('turnover', 'N/A')}")
                    print(f"   标签: {stock.get('reason', 'N/A')}")
                    print()
                    
            else:
                print("没有获取到热门股票数据")
                
        else:
            print(f"API调用失败: {response.text}")
            
    except Exception as e:
        print(f"调用API时出错: {e}")

if __name__ == "__main__":
    test_hot_stocks_api()
