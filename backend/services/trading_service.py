from typing import List, Dict, Any, Optional
from datetime import datetime
import random
from services.cache_service import cache_service

class TradingService:
    """交易服务"""
    
    def __init__(self):
        self.cache = cache_service
    
    async def create_order(self, order_data: dict) -> Dict[str, Any]:
        """创建订单"""
        order = {
            "id": random.randint(1000, 9999),
            "user_id": order_data.get("user_id"),
            "symbol": order_data.get("symbol"),
            "side": order_data.get("side"),
            "order_type": order_data.get("order_type"),
            "quantity": order_data.get("quantity"),
            "price": order_data.get("price"),
            "status": "pending",
            "created_at": datetime.now().isoformat()
        }
        return order
    
    async def get_orders(self, user_id: int, status: Optional[str] = None, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取订单列表"""
        mock_orders = [
            {"id": 1, "code": "000858", "name": "五粮液", "type": "buy", "quantity": 200, "price": 148.50, "status": "pending", "time": "09:30:15"},
            {"id": 2, "code": "002594", "name": "比亚迪", "type": "sell", "quantity": 100, "price": 245.67, "status": "filled", "time": "10:15:32"},
            {"id": 3, "code": "300750", "name": "宁德时代", "type": "buy", "quantity": 50, "price": 189.45, "status": "cancelled", "time": "11:22:18"}
        ]
        
        return {
            "list": mock_orders,
            "total": len(mock_orders),
            "page": page,
            "size": size
        }
    
    async def cancel_order(self, order_id: int) -> Dict[str, Any]:
        """取消订单"""
        return {"order_id": order_id, "status": "cancelled"}
    
    async def get_positions(self, user_id: int) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        return [
            {"code": "000001", "name": "平安银行", "quantity": 1000, "avg_price": 11.80, "current_price": 12.45, "profit": 650, "profit_percent": 5.51},
            {"code": "600519", "name": "贵州茅台", "quantity": 100, "avg_price": 1691.25, "current_price": 1678.90, "profit": -1235, "profit_percent": -0.73},
            {"code": "600036", "name": "招商银行", "quantity": 500, "avg_price": 33.33, "current_price": 34.56, "profit": 615, "profit_percent": 3.69}
        ]
    
    async def get_transactions(self, user_id: int, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取交易记录"""
        mock_transactions = [
            {"id": 1, "code": "000001", "name": "平安银行", "type": "buy", "quantity": 1000, "price": 11.80, "amount": 11800, "time": "2024-01-15 09:30:00"},
            {"id": 2, "code": "600036", "name": "招商银行", "type": "buy", "quantity": 500, "price": 33.33, "amount": 16665, "time": "2024-01-12 10:15:00"},
            {"id": 3, "code": "002594", "name": "比亚迪", "type": "sell", "quantity": 100, "price": 245.67, "amount": 24567, "time": "2024-01-10 14:30:00"}
        ]
        
        return {
            "list": mock_transactions,
            "total": len(mock_transactions),
            "page": page,
            "size": size
        }
    
    async def get_account_info(self, user_id: int) -> Dict[str, Any]:
        """获取账户信息"""
        return {
            "user_id": user_id,
            "total_assets": 1000000.0,
            "available_cash": 500000.0,
            "market_value": 500000.0,
            "profit_loss": 25000.0,
            "profit_loss_percent": 2.5
        }
    
    async def get_market_depth(self, symbol: str) -> Dict[str, Any]:
        """获取市场深度数据"""
        buy_orders = [
            {"price": 12.40, "volume": 1200},
            {"price": 12.39, "volume": 800},
            {"price": 12.38, "volume": 1500},
            {"price": 12.37, "volume": 900},
            {"price": 12.36, "volume": 2000}
        ]
        
        sell_orders = [
            {"price": 12.46, "volume": 1100},
            {"price": 12.47, "volume": 1300},
            {"price": 12.48, "volume": 700},
            {"price": 12.49, "volume": 1600},
            {"price": 12.50, "volume": 1000}
        ]
        
        return {
            "symbol": symbol,
            "buy_orders": buy_orders,
            "sell_orders": sell_orders
        } 