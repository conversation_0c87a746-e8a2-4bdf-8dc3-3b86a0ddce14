from typing import List, Dict, Any, Optional
from datetime import datetime

class UserService:
    """用户服务"""
    
    def __init__(self):
        pass
    
    async def get_user_profile(self, user_id: int) -> Dict[str, Any]:
        """获取用户信息"""
        return {
            "user_id": user_id,
            "username": "demo_user",
            "email": "<EMAIL>",
            "nickname": "演示用户",
            "avatar": "",
            "phone": "138****8888",
            "created_at": "2024-01-01",
            "last_login": datetime.now().isoformat()
        }
    
    async def update_user_profile(self, profile_data: dict) -> Dict[str, Any]:
        """更新用户信息"""
        return {
            "user_id": profile_data.get("user_id"),
            "nickname": profile_data.get("nickname"),
            "email": profile_data.get("email"),
            "phone": profile_data.get("phone"),
            "updated_at": datetime.now().isoformat()
        }
    
    async def get_user_settings(self, user_id: int) -> Dict[str, Any]:
        """获取用户设置"""
        return {
            "user_id": user_id,
            "trading_settings": {
                "auto_trading": False,
                "risk_level": "medium",
                "max_position_ratio": 0.1,
                "stop_loss_ratio": 0.05
            },
            "notification_settings": {
                "email_notifications": True,
                "sms_notifications": False,
                "push_notifications": True,
                "trading_alerts": True,
                "price_alerts": True
            },
            "display_settings": {
                "theme": "dark",
                "language": "zh-CN",
                "decimal_places": 2,
                "refresh_interval": 3
            }
        }
    
    async def update_user_settings(self, settings_data: dict) -> Dict[str, Any]:
        """更新用户设置"""
        return {
            "user_id": settings_data.get("user_id"),
            "settings": settings_data.get("settings"),
            "updated_at": datetime.now().isoformat()
        }
    
    async def get_user_watchlist(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户自选股"""
        return [
            {"symbol": "000001", "name": "平安银行", "added_at": "2024-01-10"},
            {"symbol": "600519", "name": "贵州茅台", "added_at": "2024-01-12"},
            {"symbol": "600036", "name": "招商银行", "added_at": "2024-01-15"},
            {"symbol": "002594", "name": "比亚迪", "added_at": "2024-01-18"},
            {"symbol": "300750", "name": "宁德时代", "added_at": "2024-01-20"}
        ]
    
    async def add_to_watchlist(self, watchlist_data: dict) -> Dict[str, Any]:
        """添加自选股"""
        return {
            "user_id": watchlist_data.get("user_id"),
            "symbol": watchlist_data.get("symbol"),
            "name": watchlist_data.get("name"),
            "added_at": datetime.now().isoformat()
        }
    
    async def remove_from_watchlist(self, user_id: int, symbol: str) -> Dict[str, Any]:
        """移除自选股"""
        return {
            "user_id": user_id,
            "symbol": symbol,
            "removed_at": datetime.now().isoformat()
        } 