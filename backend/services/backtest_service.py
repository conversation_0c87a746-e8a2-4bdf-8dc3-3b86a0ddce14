from typing import Dict, Any, Optional
from datetime import datetime
import random
import logging
import akshare as ak
import numpy as np

logger = logging.getLogger(__name__)

class BacktestService:
    """回测服务"""
    
    def __init__(self):
        pass
    
    async def create_backtest(self, backtest_data: dict) -> Dict[str, Any]:
        """创建回测任务"""
        backtest = {
            "id": random.randint(1000, 9999),
            "name": backtest_data.get("name"),
            "strategy_id": backtest_data.get("strategy_id"),
            "user_id": backtest_data.get("user_id"),
            "start_date": backtest_data.get("start_date"),
            "end_date": backtest_data.get("end_date"),
            "initial_capital": backtest_data.get("initial_capital", 1000000),
            "stock_pool": backtest_data.get("stock_pool", []),
            "status": "running",
            "created_at": datetime.now().isoformat()
        }
        return backtest
    
    async def get_backtests(self, user_id: int, status: Optional[str] = None, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取回测列表"""
        mock_backtests = [
            {
                "id": 1,
                "name": "均线突破策略",
                "strategy": "双均线交叉",
                "period": "2023-01-01 至 2023-12-31",
                "total_return": 15.67,
                "annual_return": 15.67,
                "max_drawdown": -8.23,
                "sharpe_ratio": 1.45,
                "win_rate": 62.5,
                "total_trades": 156,
                "avg_holding_days": 12.3,
                "status": "completed",
                "create_time": "2024-01-15 10:30:00"
            },
            {
                "id": 2,
                "name": "RSI反转策略",
                "strategy": "RSI超买超卖",
                "period": "2023-01-01 至 2023-12-31",
                "total_return": -3.45,
                "annual_return": -3.45,
                "max_drawdown": -12.56,
                "sharpe_ratio": 0.78,
                "win_rate": 45.2,
                "total_trades": 89,
                "avg_holding_days": 8.7,
                "status": "completed",
                "create_time": "2024-01-14 14:20:00"
            },
            {
                "id": 3,
                "name": "动量策略",
                "strategy": "价格动量",
                "period": "2023-01-01 至 2023-12-31",
                "total_return": 23.89,
                "annual_return": 23.89,
                "max_drawdown": -15.67,
                "sharpe_ratio": 1.89,
                "win_rate": 58.3,
                "total_trades": 203,
                "avg_holding_days": 6.8,
                "status": "running",
                "create_time": "2024-01-16 09:15:00"
            }
        ]
        
        return {
            "list": mock_backtests,
            "total": len(mock_backtests),
            "page": page,
            "size": size
        }
    
    async def get_backtest_result(self, backtest_id: int) -> Dict[str, Any]:
        """获取回测结果"""
        return {
            "backtest_id": backtest_id,
            "total_return": 15.67,
            "annual_return": 15.67,
            "max_drawdown": -8.23,
            "sharpe_ratio": 1.45,
            "win_rate": 62.5,
            "total_trades": 156,
            "avg_holding_days": 12.3,
            "profit_trades": 98,
            "loss_trades": 58,
            "avg_profit": 3.2,
            "avg_loss": -2.1,
            "profit_loss_ratio": 1.52,
            "max_consecutive_wins": 8,
            "max_consecutive_losses": 5
        }
    
    async def get_backtest_analysis(self, backtest_id: int) -> Dict[str, Any]:
        """获取详细回测分析"""
        try:
            # 获取历史数据进行回测分析

            # 获取上证指数历史数据作为基准
            benchmark_df = ak.stock_zh_index_daily(symbol="sh000001")
            benchmark_df = benchmark_df.tail(252)  # 最近一年交易日

            # 计算基准收益率
            benchmark_returns = benchmark_df['close'].pct_change().fillna(0)
            benchmark_cumulative = (1 + benchmark_returns).cumprod()

            # 模拟策略收益（基于基准收益加上策略alpha）
            strategy_alpha = 0.02  # 策略年化超额收益2%
            daily_alpha = strategy_alpha / 252
            strategy_returns = benchmark_returns + np.random.normal(daily_alpha, 0.01, len(benchmark_returns))
            strategy_cumulative = (1 + strategy_returns).cumprod()

            # 准备数据
            dates = benchmark_df.index.strftime("%Y-%m-%d").tolist()
            benchmark_values = (benchmark_cumulative * 100000).round(2).tolist()
            strategy_values = (strategy_cumulative * 100000).round(2).tolist()

            # 计算风险指标
            strategy_volatility = strategy_returns.std() * np.sqrt(252)
            benchmark_volatility = benchmark_returns.std() * np.sqrt(252)

            # 计算月度收益
            monthly_returns = []
            for i in range(0, len(strategy_returns), 21):  # 每21个交易日约为一个月
                month_returns = strategy_returns[i:i+21]
                if len(month_returns) > 0:
                    monthly_return = (1 + month_returns).prod() - 1
                    monthly_returns.append(round(monthly_return, 4))

            # 计算交易统计
            total_trades = 156
            win_rate = 0.625
            profit_trades = int(total_trades * win_rate)
            loss_trades = total_trades - profit_trades

            return {
                "backtest_id": backtest_id,
                "net_value_curve": {
                    "dates": dates,
                    "strategy_values": strategy_values,
                    "benchmark_values": benchmark_values
                },
                "monthly_returns": monthly_returns[:12],  # 取前12个月
                "risk_metrics": {
                    "volatility": round(strategy_volatility, 3),
                    "beta": round(strategy_volatility / benchmark_volatility, 3),
                    "alpha": round(strategy_alpha, 3),
                    "information_ratio": round(strategy_alpha / (strategy_volatility - benchmark_volatility), 2),
                    "calmar_ratio": round(strategy_alpha / 0.08, 2),  # 假设最大回撤8%
                    "sortino_ratio": round(strategy_alpha / (strategy_returns[strategy_returns < 0].std() * np.sqrt(252)), 2),
                    "var_95": round(np.percentile(strategy_returns, 5), 3)
                },
                "trade_analysis": {
                    "total_trades": total_trades,
                    "profit_trades": profit_trades,
                    "loss_trades": loss_trades,
                    "avg_profit_per_trade": 3.2,
                    "avg_loss_per_trade": -2.1,
                    "largest_profit": 15.6,
                    "largest_loss": -8.9,
                    "profit_loss_ratio": 1.52,
                    "max_consecutive_wins": 8,
                    "max_consecutive_losses": 5
                }
            }

        except Exception as e:
            logger.error(f"获取回测分析失败: {e}")
            raise Exception(f"回测分析数据源不可用: {str(e)}")
    
    async def delete_backtest(self, backtest_id: int) -> Dict[str, Any]:
        """删除回测"""
        return {
            "backtest_id": backtest_id,
            "deleted": True,
            "deleted_at": datetime.now().isoformat()
        }
    
    async def compare_backtests(self, compare_data: dict) -> Dict[str, Any]:
        """对比回测结果"""
        backtest_ids = compare_data.get("backtest_ids", [])
        
        comparison = {
            "backtest_ids": backtest_ids,
            "comparison_metrics": [
                {
                    "backtest_id": backtest_ids[0] if backtest_ids else 1,
                    "name": "均线突破策略",
                    "total_return": 15.67,
                    "sharpe_ratio": 1.45,
                    "max_drawdown": -8.23,
                    "win_rate": 62.5
                },
                {
                    "backtest_id": backtest_ids[1] if len(backtest_ids) > 1 else 2,
                    "name": "RSI反转策略",
                    "total_return": -3.45,
                    "sharpe_ratio": 0.78,
                    "max_drawdown": -12.56,
                    "win_rate": 45.2
                }
            ],
            "comparison_chart_data": {
                "dates": ["2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06"],
                "strategy1_returns": [0.02, 0.015, -0.01, 0.03, 0.008, 0.012],
                "strategy2_returns": [-0.005, 0.01, -0.02, 0.015, -0.012, 0.008]
            }
        }
        
        return comparison
    
    async def get_backtest_report(self, backtest_id: int) -> Dict[str, Any]:
        """获取回测报告"""
        return {
            "backtest_id": backtest_id,
            "report_title": f"回测报告 #{backtest_id}",
            "strategy_name": "均线突破策略",
            "backtest_period": "2023-01-01 至 2023-12-31",
            "initial_capital": 1000000,
            "final_capital": 1156700,
            "summary": {
                "total_return": "15.67%",
                "annual_return": "15.67%",
                "max_drawdown": "-8.23%",
                "sharpe_ratio": "1.45",
                "win_rate": "62.5%",
                "total_trades": 156
            },
            "strategy_description": "该策略基于5日和20日移动平均线的交叉信号进行交易，当短期均线上穿长期均线时买入，下穿时卖出。",
            "performance_analysis": "策略在回测期间表现良好，获得了15.67%的年化收益率，夏普比率为1.45，表明风险调整后的收益较为理想。最大回撤控制在8.23%以内，风险控制得当。",
            "risk_analysis": "策略的波动率适中，贝塔系数接近1，与市场相关性较高。VaR指标显示95%置信度下的最大日损失约为2.5%。",
            "improvement_suggestions": [
                "可以考虑加入成交量确认信号，提高交易质量",
                "建议设置止损机制，进一步控制单笔交易风险",
                "可以尝试优化均线参数，寻找更适合的周期组合"
            ],
            "generated_at": datetime.now().isoformat()
        } 