from typing import List, Dict, Any, Optional
from datetime import datetime
import random

class StrategyService:
    """策略服务"""
    
    def __init__(self):
        pass
    
    async def get_user_strategies(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户策略列表"""
        return [
            {
                "id": 1,
                "name": "均线突破策略",
                "description": "基于移动平均线的突破交易策略",
                "status": "running",
                "profit": 12580.50,
                "profit_percent": 8.45,
                "trades": 156,
                "win_rate": 68.5,
                "created_at": "2024-01-15",
                "code": "# 均线突破策略示例代码"
            },
            {
                "id": 2,
                "name": "RSI超买超卖策略",
                "description": "基于RSI指标的反转交易策略",
                "status": "stopped",
                "profit": -2340.20,
                "profit_percent": -1.56,
                "trades": 89,
                "win_rate": 45.2,
                "created_at": "2024-01-10",
                "code": "# RSI策略示例代码"
            }
        ]
    
    async def create_strategy(self, strategy_data: dict) -> Dict[str, Any]:
        """创建新策略"""
        strategy = {
            "id": random.randint(1000, 9999),
            "name": strategy_data.get("name"),
            "description": strategy_data.get("description"),
            "code": strategy_data.get("code"),
            "user_id": strategy_data.get("user_id"),
            "status": "stopped",
            "profit": 0.0,
            "profit_percent": 0.0,
            "trades": 0,
            "win_rate": 0.0,
            "created_at": datetime.now().isoformat()
        }
        return strategy
    
    async def update_strategy(self, strategy_id: int, strategy_data: dict) -> Dict[str, Any]:
        """更新策略"""
        return {
            "id": strategy_id,
            "name": strategy_data.get("name"),
            "description": strategy_data.get("description"),
            "code": strategy_data.get("code"),
            "updated_at": datetime.now().isoformat()
        }
    
    async def start_strategy(self, strategy_id: int) -> Dict[str, Any]:
        """启动策略"""
        return {
            "strategy_id": strategy_id,
            "status": "running",
            "started_at": datetime.now().isoformat()
        }
    
    async def stop_strategy(self, strategy_id: int) -> Dict[str, Any]:
        """停止策略"""
        return {
            "strategy_id": strategy_id,
            "status": "stopped",
            "stopped_at": datetime.now().isoformat()
        }
    
    async def get_strategy_performance(self, strategy_id: int) -> Dict[str, Any]:
        """获取策略表现"""
        return {
            "strategy_id": strategy_id,
            "total_return": 15.67,
            "annual_return": 15.67,
            "max_drawdown": -8.23,
            "sharpe_ratio": 1.45,
            "win_rate": 62.5,
            "total_trades": 156,
            "avg_holding_days": 12.3
        }
    
    async def get_strategy_templates(self) -> List[Dict[str, Any]]:
        """获取策略模板"""
        return [
            {
                "id": 1,
                "name": "双均线策略",
                "description": "经典的双移动平均线交易策略",
                "category": "趋势跟踪",
                "difficulty": "初级",
                "downloads": 1250,
                "rating": 4.5
            },
            {
                "id": 2,
                "name": "MACD金叉死叉",
                "description": "基于MACD指标的交易策略",
                "category": "技术指标",
                "difficulty": "中级",
                "downloads": 890,
                "rating": 4.2
            },
            {
                "id": 3,
                "name": "布林带突破",
                "description": "利用布林带通道的突破策略",
                "category": "波动率",
                "difficulty": "中级",
                "downloads": 670,
                "rating": 4.0
            },
            {
                "id": 4,
                "name": "量价配合策略",
                "description": "结合成交量和价格的综合策略",
                "category": "量价分析",
                "difficulty": "高级",
                "downloads": 450,
                "rating": 4.7
            }
        ] 