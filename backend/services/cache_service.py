import redis
import json
import asyncio
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
from config.settings import get_settings

settings = get_settings()

class CacheService:
    """Redis缓存服务"""
    
    def __init__(self):
        self.redis_client = None
        self._connect()
    
    def _connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            # 测试连接
            self.redis_client.ping()
            print("Redis连接成功")
        except Exception as e:
            print(f"Redis连接失败，使用内存缓存: {e}")
            self.redis_client = None
            self._memory_cache = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            if self.redis_client:
                data = self.redis_client.get(key)
                if data:
                    return json.loads(data)
            else:
                # 内存缓存
                if key in self._memory_cache:
                    item = self._memory_cache[key]
                    if datetime.now() < item['expires']:
                        return item['data']
                    else:
                        del self._memory_cache[key]
            return None
        except Exception as e:
            print(f"获取缓存失败 {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存数据"""
        try:
            if self.redis_client:
                data = json.dumps(value, ensure_ascii=False, default=str)
                self.redis_client.setex(key, ttl, data)
            else:
                # 内存缓存
                self._memory_cache[key] = {
                    'data': value,
                    'expires': datetime.now() + timedelta(seconds=ttl)
                }
            return True
        except Exception as e:
            print(f"设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if self.redis_client:
                self.redis_client.delete(key)
            else:
                if key in self._memory_cache:
                    del self._memory_cache[key]
            return True
        except Exception as e:
            print(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            if self.redis_client:
                return bool(self.redis_client.exists(key))
            else:
                if key in self._memory_cache:
                    item = self._memory_cache[key]
                    if datetime.now() < item['expires']:
                        return True
                    else:
                        del self._memory_cache[key]
                return False
        except Exception as e:
            print(f"检查缓存失败 {key}: {e}")
            return False
    
    async def get_or_set(self, key: str, func, ttl: int = 3600, *args, **kwargs) -> Any:
        """获取缓存，如果不存在则执行函数并缓存结果"""
        data = await self.get(key)
        if data is not None:
            return data
        
        # 执行函数获取数据
        if asyncio.iscoroutinefunction(func):
            result = await func(*args, **kwargs)
        else:
            result = func(*args, **kwargs)
        
        # 缓存结果
        await self.set(key, result, ttl)
        return result
    
    async def mget(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存"""
        result = {}
        for key in keys:
            result[key] = await self.get(key)
        return result
    
    async def mset(self, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """批量设置缓存"""
        try:
            for key, value in data.items():
                await self.set(key, value, ttl)
            return True
        except Exception as e:
            print(f"批量设置缓存失败: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    return self.redis_client.delete(*keys)
            else:
                # 内存缓存模式匹配
                import fnmatch
                keys_to_delete = [key for key in self._memory_cache.keys() 
                                if fnmatch.fnmatch(key, pattern)]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                return len(keys_to_delete)
            return 0
        except Exception as e:
            print(f"清除缓存失败 {pattern}: {e}")
            return 0
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    "type": "redis",
                    "connected": True,
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0)
                }
            else:
                return {
                    "type": "memory",
                    "connected": True,
                    "cache_size": len(self._memory_cache),
                    "memory_usage": "N/A"
                }
        except Exception as e:
            return {
                "type": "unknown",
                "connected": False,
                "error": str(e)
            }
    
    async def warmup_cache(self):
        """缓存预热"""
        try:
            print("开始缓存预热...")
            
            # 预热热门股票列表
            hot_stocks = ["000001", "600519", "600036", "002594", "300750", "000002", "000858"]
            
            # 这里可以预加载一些常用数据
            warmup_data = {
                "hot_stocks": hot_stocks,
                "market_status": "open",
                "last_warmup": datetime.now().isoformat()
            }
            
            await self.set("cache:warmup", warmup_data, ttl=3600)
            print("缓存预热完成")
            
        except Exception as e:
            print(f"缓存预热失败: {e}")
    
    def get_cache_key(self, prefix: str, *args) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(arg) for arg in args]
        return ":".join(key_parts)

# 全局缓存服务实例
cache_service = CacheService() 