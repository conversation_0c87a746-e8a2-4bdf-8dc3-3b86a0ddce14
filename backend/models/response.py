from typing import Any, Optional, Dict
from datetime import datetime
from pydantic import BaseModel

class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """成功响应"""
        return {
            "code": 200,
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 500, data: Any = None) -> Dict[str, Any]:
        """错误响应"""
        return {
            "code": code,
            "success": False,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }

class StockInfo(BaseModel):
    """股票信息模型"""
    code: str
    name: str
    price: float
    change: float
    change_percent: float
    volume: str
    turnover: str
    high: float
    low: float
    open: float
    market_cap: str
    pe: Optional[float] = None
    pb: Optional[float] = None

class OrderInfo(BaseModel):
    """订单信息模型"""
    id: Optional[int] = None
    user_id: int
    symbol: str
    side: str  # buy/sell
    order_type: str  # limit/market
    quantity: int
    price: Optional[float] = None
    status: str = "pending"
    created_at: Optional[datetime] = None

class PositionInfo(BaseModel):
    """持仓信息模型"""
    symbol: str
    name: str
    quantity: int
    avg_price: float
    current_price: float
    profit: float
    profit_percent: float
    market_value: float

class StrategyInfo(BaseModel):
    """策略信息模型"""
    id: Optional[int] = None
    name: str
    description: str
    code: str
    user_id: int
    status: str = "stopped"
    profit: float = 0.0
    profit_percent: float = 0.0
    trades: int = 0
    win_rate: float = 0.0
    created_at: Optional[datetime] = None

class BacktestInfo(BaseModel):
    """回测信息模型"""
    id: Optional[int] = None
    name: str
    strategy_id: int
    user_id: int
    start_date: str
    end_date: str
    initial_capital: float
    stock_pool: list
    status: str = "pending"
    total_return: Optional[float] = None
    annual_return: Optional[float] = None
    max_drawdown: Optional[float] = None
    sharpe_ratio: Optional[float] = None
    win_rate: Optional[float] = None
    total_trades: Optional[int] = None
    created_at: Optional[datetime] = None 