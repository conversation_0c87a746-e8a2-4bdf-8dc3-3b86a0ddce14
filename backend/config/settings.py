from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List
import os

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用配置
    APP_NAME: str = "AI量化交易系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 跨域配置
    CORS_ORIGINS: List[str] = ["http://localhost:3088", "http://127.0.0.1:3088"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./data/ai_trader.db"
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    
    # 数据存储路径
    DATA_DIR: str = "./data"
    FEATHER_DIR: str = "./data/feather"
    LOG_DIR: str = "./logs"
    
    # 缓存配置
    CACHE_TTL_REALTIME: int = 3  # 实时数据缓存3秒
    CACHE_TTL_KLINE: int = 60    # K线数据缓存60秒
    CACHE_TTL_DAILY: int = 3600  # 日线数据缓存1小时
    
    # 数据源配置
    AKSHARE_ENABLED: bool = True
    LONGPORT_ENABLED: bool = False
    LONGPORT_APP_KEY: str = ""
    LONGPORT_APP_SECRET: str = ""
    LONGPORT_ACCESS_TOKEN: str = ""
    
    # 交易配置
    TRADING_ENABLED: bool = False  # 是否启用真实交易
    PAPER_TRADING: bool = True     # 模拟交易
    INITIAL_CAPITAL: float = 1000000.0  # 初始资金
    
    # 风控配置
    MAX_POSITION_RATIO: float = 0.1  # 单只股票最大仓位比例
    MAX_DAILY_LOSS: float = 0.05     # 日最大亏损比例
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30  # 心跳间隔(秒)
    WS_MAX_CONNECTIONS: int = 100    # 最大连接数
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
_settings = None

def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = Settings()
        
        # 确保数据目录存在
        os.makedirs(_settings.DATA_DIR, exist_ok=True)
        os.makedirs(_settings.FEATHER_DIR, exist_ok=True)
        os.makedirs(_settings.LOG_DIR, exist_ok=True)
    
    return _settings 