#!/usr/bin/env python3
"""
测试监控大屏API的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.market_service import MarketService

async def test_dashboard_data():
    """测试监控大屏数据获取"""
    market_service = MarketService()
    
    print("开始测试监控大屏数据获取...")
    
    try:
        # 测试全球指数数据
        print("\n1. 测试全球指数数据...")
        global_indices = await market_service.get_global_indices()
        print(f"全球指数数据类型: {type(global_indices)}")
        print(f"全球指数数据: {global_indices}")
        
        # 测试热门股票
        print("\n2. 测试热门股票...")
        hot_stocks = await market_service.get_hot_stocks()
        print(f"热门股票数据类型: {type(hot_stocks)}")
        print(f"热门股票数据: {hot_stocks}")
        
        # 测试商品期货数据
        print("\n3. 测试商品期货数据...")
        commodities = await market_service.get_commodities_data()
        print(f"商品期货数据类型: {type(commodities)}")
        print(f"商品期货数据: {commodities}")
        
        # 测试外汇数据
        print("\n4. 测试外汇数据...")
        forex = await market_service.get_forex_data()
        print(f"外汇数据类型: {type(forex)}")
        print(f"外汇数据: {forex}")
        
        # 测试板块数据
        print("\n5. 测试板块数据...")
        sectors = await market_service.get_sectors_data()
        print(f"板块数据类型: {type(sectors)}")
        print(f"板块数据: {sectors}")

        # 测试市场统计
        print("\n6. 测试市场统计...")
        market_stats = await market_service.get_market_statistics()
        print(f"市场统计数据类型: {type(market_stats)}")
        print(f"市场统计数据: {market_stats}")
        
        # 测试风险监控指标
        print("\n7. 测试风险监控指标...")
        risk_indicators = await market_service.get_risk_indicators()
        print(f"风险监控指标数据类型: {type(risk_indicators)}")
        print(f"风险监控指标数据: {risk_indicators}")
        
        print("\n所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_dashboard_data())
