.market-hotspots {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.refresh-btn {
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #40a9ff;
  transform: rotate(180deg);
}

.hotspots-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.rankings-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.popularity-ranking,
.turnover-ranking {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.popularity-ranking h4,
.turnover-ranking h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-size: 16px;
  color: #ffffff;
}

.popularity-ranking h4 i {
  color: #ff7875;
}

.turnover-ranking h4 i {
  color: #faad14;
}

.stock-turnover {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}

.gainers-losers {
  display: flex;
  gap: 20px;
}

.gainers,
.losers {
  flex: 1;
}

.gainers h4,
.losers h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-size: 16px;
}

.stocks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stock-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stock-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(64, 169, 255, 0.3);
  transform: translateX(5px);
}

.stock-rank {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  color: #40a9ff;
}

.stock-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stock-name {
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

.stock-code {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.stock-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.stock-price {
  font-size: 14px;
  color: #ffffff;
  font-weight: bold;
}

.stock-change {
  font-size: 12px;
  font-weight: bold;
}

.sector-flow {
  margin-top: 10px;
}

.sector-flow h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  color: #ffffff;
  font-size: 16px;
}

.sector-flow h4 i {
  color: #40a9ff;
}

.flow-chart {
  margin-bottom: 15px;
}

.flow-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.flow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.flow-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.sector-name {
  font-size: 14px;
  color: #ffffff;
}

.flow-value {
  font-size: 14px;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .gainers-losers {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .stock-item {
    padding: 8px 10px;
  }
  
  .stock-name {
    font-size: 12px;
  }
  
  .stock-code {
    font-size: 10px;
  }
  
  .stock-price {
    font-size: 12px;
  }
  
  .stock-change {
    font-size: 10px;
  }
}
