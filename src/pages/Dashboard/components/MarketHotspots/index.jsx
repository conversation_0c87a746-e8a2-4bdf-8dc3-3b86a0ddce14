import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import './index.css';

const MarketHotspots = () => {
  const [popularityStocks, setPopularityStocks] = useState([]);
  const [turnoverStocks, setTurnoverStocks] = useState([]);
  const [sectors, setSectors] = useState([]);
  const [loading, setLoading] = useState(true);

  // 获取人气榜数据
  const fetchPopularityStocks = async () => {
    try {
      const response = await fetch('/api/market/popularity-ranking?limit=30');
      const result = await response.json();
      if (result.success) {
        setPopularityStocks(result.data || []);
      }
    } catch (error) {
      console.error('获取人气榜数据失败:', error);
    }
  };

  // 获取成交额榜数据
  const fetchTurnoverStocks = async () => {
    try {
      const response = await fetch('/api/market/turnover-ranking?limit=30');
      const result = await response.json();
      if (result.success) {
        setTurnoverStocks(result.data || []);
      }
    } catch (error) {
      console.error('获取成交额榜数据失败:', error);
    }
  };

  // 获取板块数据
  const fetchSectors = async () => {
    try {
      const response = await fetch('/api/market/sectors');
      const result = await response.json();
      if (result.success) {
        setSectors(result.data || []);
      }
    } catch (error) {
      console.error('获取板块数据失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchPopularityStocks(), fetchTurnoverStocks(), fetchSectors()]);
      setLoading(false);
    };
    loadData();
  }, []);

  // 处理人气榜数据（显示前10名）
  const topPopularityStocks = popularityStocks.slice(0, 10).map(stock => ({
    name: stock.name,
    code: stock.code,
    price: stock.current || stock.price,
    change: stock.change,
    changePercent: stock.change_percent,
    rank: stock.rank,
    turnover: stock.turnover
  }));

  // 处理成交额榜数据（显示前10名）
  const topTurnoverStocks = turnoverStocks.slice(0, 10).map(stock => ({
    name: stock.name,
    code: stock.code,
    price: stock.current || stock.price,
    change: stock.change,
    changePercent: stock.change_percent,
    rank: stock.rank,
    turnover: stock.turnover
  }));

  // 临时保留原有变量以避免错误
  const topGainers = topPopularityStocks.filter(stock => stock.changePercent > 0).slice(0, 5);
  const topLosers = topPopularityStocks.filter(stock => stock.changePercent < 0).slice(0, 5);

  // 板块资金流向数据
  const sectorData = sectors.length > 0 ? sectors.map(sector => ({
    name: sector.name,
    flow: sector.change_percent * 10, // 模拟资金流向
    change: sector.change_percent
  })) : [
    { name: '新能源', flow: 156.78, change: 8.45 },
    { name: '半导体', flow: -89.34, change: -3.21 },
    { name: '医药生物', flow: 67.89, change: 2.34 }
  ];

  const getFlowChartOption = () => {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}亿 ({d}%)'
      },
      series: [
        {
          name: '资金流向',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: sectorData.map(item => ({
            value: Math.abs(item.flow),
            name: item.name,
            itemStyle: {
              color: item.flow > 0 ? '#00d4aa' : '#ff4757'
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: '#ffffff',
            fontSize: 12
          }
        }
      ]
    };
  };

  return (
    <div className="market-hotspots">
      <div className="section-header">
        <h3>
          <i className="fas fa-fire"></i>
          市场热点
        </h3>
        <div
          className="refresh-btn"
          onClick={async () => {
            setLoading(true);
            await Promise.all([fetchPopularityStocks(), fetchTurnoverStocks(), fetchSectors()]);
            setLoading(false);
          }}
        >
          <i className={`fas fa-sync-alt ${loading ? 'fa-spin' : ''}`}></i>
        </div>
      </div>

      {loading && (
        <div className="loading-indicator" style={{textAlign: 'center', padding: '20px', color: '#888'}}>
          <i className="fas fa-spinner fa-spin"></i> 加载中...
        </div>
      )}

      {!loading && <div className="hotspots-content">
        <div className="rankings-section">
          <div className="popularity-ranking">
            <h4 className="text-primary">
              <i className="fas fa-fire"></i>
              人气榜 TOP30
            </h4>
            <div className="stocks-list">
              {topPopularityStocks.map((stock, index) => (
                <div key={index} className="stock-item">
                  <div className="stock-rank">{stock.rank || index + 1}</div>
                  <div className="stock-info">
                    <div className="stock-name">{stock.name}</div>
                    <div className="stock-code">{stock.code}</div>
                  </div>
                  <div className="stock-data">
                    <div className="stock-price">¥{stock.price}</div>
                    <div className={`stock-change ${stock.changePercent >= 0 ? 'text-up' : 'text-down'}`}>
                      {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="turnover-ranking">
            <h4 className="text-warning">
              <i className="fas fa-chart-bar"></i>
              成交额榜 TOP30
            </h4>
            <div className="stocks-list">
              {topTurnoverStocks.map((stock, index) => (
                <div key={index} className="stock-item">
                  <div className="stock-rank">{stock.rank || index + 1}</div>
                  <div className="stock-info">
                    <div className="stock-name">{stock.name}</div>
                    <div className="stock-code">{stock.code}</div>
                  </div>
                  <div className="stock-data">
                    <div className="stock-price">¥{stock.price}</div>
                    <div className="stock-turnover">{stock.turnover}</div>
                    <div className={`stock-change ${stock.changePercent >= 0 ? 'text-up' : 'text-down'}`}>
                      {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="sector-flow">
          <h4>
            <i className="fas fa-chart-pie"></i>
            板块资金流向
          </h4>
          <div className="flow-chart">
            <ReactECharts 
              option={getFlowChartOption()} 
              style={{ height: '200px' }}
              theme="dark"
            />
          </div>
          <div className="flow-list">
            {sectorData.map((sector, index) => (
              <div key={index} className="flow-item">
                <span className="sector-name">{sector.name}</span>
                <span className={`flow-value ${sector.flow > 0 ? 'text-up' : 'text-down'}`}>
                  {sector.flow > 0 ? '+' : ''}{sector.flow.toFixed(2)}亿
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>}
    </div>
  );
};

export default MarketHotspots;
