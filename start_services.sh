#!/bin/bash

# AI量化交易系统 - 服务启动脚本
# 作者: AI Assistant
# 日期: 2024-06-24

set -e  # 遇到错误立即退出

echo "🚀 AI量化交易系统 - 服务启动脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/Users/<USER>/Documents/quant/AI_Trader_V1.0"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查并杀死占用端口的进程
kill_port_processes() {
    local port=$1
    local service_name=$2
    
    log_info "检查端口 $port 是否被占用..."
    
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        log_warning "发现端口 $port 被以下进程占用: $pids"
        log_info "正在杀死 $service_name 相关进程..."
        echo $pids | xargs kill -9 2>/dev/null || true
        sleep 2
        log_success "端口 $port 已释放"
    else
        log_info "端口 $port 未被占用"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        log_error "Python 未安装或不在PATH中"
        exit 1
    fi
    log_success "Python: $(python --version)"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装或不在PATH中"
        exit 1
    fi
    log_success "Node.js: $(node --version)"
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    log_success "项目目录检查通过"
}

# 清理所有相关进程
cleanup_processes() {
    log_info "清理所有相关进程..."
    
    # 杀死Python进程（后端）
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    
    # 杀死Node.js进程（前端）
    pkill -f "webpack.*serve" 2>/dev/null || true
    pkill -f "webpack-dev-server" 2>/dev/null || true
    
    # 杀死端口占用进程
    kill_port_processes 8000 "后端服务"
    kill_port_processes 3088 "前端服务"
    
    log_success "进程清理完成"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    cd "$BACKEND_DIR"
    
    # 检查后端依赖
    if [ ! -f "requirements.txt" ]; then
        log_error "后端依赖文件 requirements.txt 不存在"
        exit 1
    fi
    
    # 检查main.py
    if [ ! -f "main.py" ]; then
        log_error "后端入口文件 main.py 不存在"
        exit 1
    fi
    
    log_info "启动后端服务 (端口: 8000)..."
    nohup python main.py > backend.log 2>&1 &
    BACKEND_PID=$!
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            log_success "后端服务启动成功 (PID: $BACKEND_PID)"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    log_error "后端服务启动失败，请检查日志: $BACKEND_DIR/backend.log"
    exit 1
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    cd "$FRONTEND_DIR"
    
    # 检查前端依赖
    if [ ! -f "package.json" ]; then
        log_error "前端依赖文件 package.json 不存在"
        exit 1
    fi
    
    # 检查webpack配置
    if [ ! -f "webpack.config.js" ]; then
        log_error "Webpack配置文件不存在"
        exit 1
    fi
    
    log_info "启动前端服务 (端口: 3088)..."
    nohup node node_modules/.bin/webpack serve > frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:3088 > /dev/null 2>&1; then
            log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    log_error "前端服务启动失败，请检查日志: $FRONTEND_DIR/frontend.log"
    exit 1
}

# 验证服务状态
verify_services() {
    log_info "验证服务状态..."
    
    # 检查后端API
    if curl -s http://localhost:8000/api/market/indices > /dev/null; then
        log_success "后端API响应正常"
    else
        log_error "后端API响应异常"
        return 1
    fi
    
    # 检查前端页面
    if curl -s http://localhost:3088 > /dev/null; then
        log_success "前端页面响应正常"
    else
        log_error "前端页面响应异常"
        return 1
    fi
    
    log_success "所有服务验证通过！"
}

# 显示服务信息
show_service_info() {
    echo ""
    echo "🎉 服务启动完成！"
    echo "=================="
    echo ""
    echo "📊 监控大屏: http://localhost:3088"
    echo "🔧 API文档:  http://localhost:8000/docs"
    echo "💾 API健康检查: http://localhost:8000/health"
    echo ""
    echo "📝 日志文件:"
    echo "   后端: $BACKEND_DIR/backend.log"
    echo "   前端: $FRONTEND_DIR/frontend.log"
    echo ""
    echo "🛑 停止服务:"
    echo "   ./stop_services.sh"
    echo ""
}

# 主函数
main() {
    # 检查依赖
    check_dependencies
    
    # 清理进程
    cleanup_processes
    
    # 等待端口释放
    sleep 3
    
    # 启动后端
    start_backend
    
    # 启动前端
    start_frontend
    
    # 验证服务
    verify_services
    
    # 显示信息
    show_service_info
}

# 执行主函数
main "$@"
