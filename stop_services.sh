#!/bin/bash

# AI量化交易系统 - 服务停止脚本
# 作者: AI Assistant
# 日期: 2024-06-24

echo "🛑 AI量化交易系统 - 服务停止脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查并杀死占用端口的进程
kill_port_processes() {
    local port=$1
    local service_name=$2
    
    log_info "检查端口 $port 是否被占用..."
    
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        log_warning "发现端口 $port 被以下进程占用: $pids"
        log_info "正在杀死 $service_name 相关进程..."
        echo $pids | xargs kill -9 2>/dev/null || true
        sleep 1
        
        # 再次检查
        local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ -z "$remaining_pids" ]; then
            log_success "端口 $port 已释放"
        else
            log_error "端口 $port 仍被占用: $remaining_pids"
        fi
    else
        log_info "端口 $port 未被占用"
    fi
}

# 停止所有相关进程
stop_all_processes() {
    log_info "停止所有相关进程..."
    
    # 杀死Python进程（后端）
    log_info "停止后端Python进程..."
    pkill -f "python.*main.py" 2>/dev/null || true
    pkill -f "uvicorn" 2>/dev/null || true
    
    # 杀死Node.js进程（前端）
    log_info "停止前端Node.js进程..."
    pkill -f "webpack.*serve" 2>/dev/null || true
    pkill -f "webpack-dev-server" 2>/dev/null || true
    
    # 等待进程结束
    sleep 2
    
    # 杀死端口占用进程
    kill_port_processes 8000 "后端服务"
    kill_port_processes 3088 "前端服务"
    
    log_success "所有进程已停止"
}

# 清理日志文件（可选）
cleanup_logs() {
    if [ "$1" = "--clean-logs" ]; then
        log_info "清理日志文件..."
        rm -f backend/backend.log 2>/dev/null || true
        rm -f frontend.log 2>/dev/null || true
        log_success "日志文件已清理"
    fi
}

# 验证停止状态
verify_stopped() {
    log_info "验证服务停止状态..."
    
    # 检查端口8000
    if lsof -ti:8000 > /dev/null 2>&1; then
        log_error "后端服务 (端口8000) 仍在运行"
        return 1
    else
        log_success "后端服务已停止"
    fi
    
    # 检查端口3088
    if lsof -ti:3088 > /dev/null 2>&1; then
        log_error "前端服务 (端口3088) 仍在运行"
        return 1
    else
        log_success "前端服务已停止"
    fi
    
    log_success "所有服务已完全停止！"
}

# 显示帮助信息
show_help() {
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --clean-logs    同时清理日志文件"
    echo "  --help         显示此帮助信息"
    echo ""
}

# 主函数
main() {
    case "$1" in
        --help)
            show_help
            exit 0
            ;;
        --clean-logs)
            stop_all_processes
            cleanup_logs --clean-logs
            verify_stopped
            ;;
        *)
            stop_all_processes
            verify_stopped
            ;;
    esac
    
    echo ""
    echo "✅ 服务停止完成！"
    echo ""
    echo "重新启动服务请运行: ./start_services.sh"
    echo ""
}

# 执行主函数
main "$@"
