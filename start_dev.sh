#!/bin/bash

echo "🚀 启动AI量化交易系统开发环境"
echo "=================================="

# 检查是否安装了Python和Node.js
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm，请先安装npm"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 安装前端依赖
echo "📦 安装前端依赖..."
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
    echo "✅ 前端依赖安装完成"
else
    echo "✅ 前端依赖已存在，跳过安装"
fi

# 安装后端依赖
# echo "📦 安装后端依赖..."
# cd backend
# if [ ! -d "venv" ]; then
#     echo "创建Python虚拟环境..."
#     python3 -m venv venv
#     source venv/bin/activate
#     pip install -r requirements.txt
#     if [ $? -ne 0 ]; then
#         echo "❌ 后端依赖安装失败"
#         exit 1
#     fi
#     echo "✅ 后端依赖安装完成"
# else
#     echo "✅ Python虚拟环境已存在"
#     source venv/bin/activate
# fi

cd ..

echo ""
echo "🎯 启动服务..."
echo ""

# 启动后端服务（后台运行）
echo "🔧 启动后端API服务 (端口8000)..."
cd /Users/<USER>/Documents/quant/AI_Trader_V1.0/backend
# source venv/bin/activate
python start.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 启动前端服务（后台运行）
echo "🎨 启动前端开发服务 (端口3088)..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 服务启动成功！"
echo "=================================="
echo "📱 前端地址: http://localhost:3088"
echo "🔗 后端API: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo ""
echo "按 Ctrl+C 停止所有服务"
echo "=================================="

# 等待用户中断
wait

# 清理进程
echo ""
echo "🛑 正在停止服务..."
kill $BACKEND_PID 2>/dev/null
kill $FRONTEND_PID 2>/dev/null
echo "✅ 服务已停止" 